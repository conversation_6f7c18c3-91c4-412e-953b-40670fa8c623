//
//  ChessGameViewModel.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/6/1.
//

import SwiftUI
import ChessKit
import Combine
import AppKit
import AVFoundation

/// ViewModel that manages the chess game UI interactions
@MainActor
final class ChessGameViewModel: ObservableObject {
    // MARK: - Session Reference
    weak var session: GameSession?
    
    // MARK: - Published Properties - derived from session
    @Published var board: Board = Board()
    @Published var currentMoveIndex: MoveTree.MoveIndex = 0
    @Published var selectedSquare: Square?
    @Published var possibleMoves: [Square] = []
    @Published var lastMove: Move?
    
    /// Board flip state
    @Published var isBoardFlipped: Bool = false

    // MARK: - Reverse Drag (target-first selection via drag)
    @Published var isReverseDragActive: Bool = false
    @Published var reverseDragTarget: Square?
    @Published var reverseDragValidSources: [Square] = []
    
    @Published var gameStatus: GameStatus = .inProgress
    @Published var promotionMove: Move?
    @Published var showPromotionDialog = false
    @Published var currentFilePath: URL? // Track current file for save functionality

    // MARK: - Variation Selection
    @Published var showVariationSelection = false
    @Published var availableVariations: [VariationOption] = []
    @Published var isKeyboardNavigationDisabled = false  // Used to temporarily disable global keyboard shortcuts

    // MARK: - Variation Creation
    @Published var showVariationCreationDialog = false
    @Published var pendingMove: Move?
    @Published var pendingMoveFromIndex: MoveTree.MoveIndex?
    @Published var existingNextMoveIndex: MoveTree.MoveIndex?
    
    /// Options for creating a new variation
    enum VariationCreationOption {
        case newVariation      // Create new variation (default behavior)
        case newMainLine       // Create new variation and promote it
        case overwrite         // Create new variation and delete existing move
    }

    // MARK: - Move Editing
    @Published var showMoveEditMenu = false
    @Published var editMenuPosition: CGPoint = .zero
    @Published var selectedMoveForEdit: MoveTree.MoveIndex?
    
    // MARK: - Position Editor
    @Published var showPositionEditor = false
    
    // MARK: - Import Error Alert
    @Published var showImportErrorAlert = false
    
    // MARK: - Child Managers
    @Published var soundManager = SoundManager()
    @Published var engineManager = EngineManager()
    @Published var visualAnnotationManager: VisualAnnotationManager
    
    // MARK: - Performance Cache
    private var _cachedMoves: [MoveDisplayItem] = []
    private var _cacheValid = false
    private var _lastCacheInvalidationTime = Date.distantPast
    private let _cacheInvalidationThreshold: TimeInterval = 0.1 // 100ms throttle
    private var _lastCurrentMoveIndex: MoveTree.MoveIndex? // Track the last current move for comparison
    
    /// Returns true if only the current move index has changed, not the game content
    var hasOnlyCurrentMoveChanged: Bool {
        defer { _lastCurrentMoveIndex = currentMoveIndex }
        return _cacheValid && _lastCurrentMoveIndex != currentMoveIndex
    }
    
    // MARK: - Move Display Types
    struct MoveDisplayItem: Identifiable {
        let id: String
        let pgnElement: MoveTree.PGNElement?
        let move: Move?
        let index: MoveTree.MoveIndex?
        let moveNumber: Int?
        let isWhiteMove: Bool?

        // Create stable ID based on content instead of random UUID
        init(pgnElement: MoveTree.PGNElement, sequenceIndex: Int) {
            self.pgnElement = pgnElement
            self.move = nil
            self.index = nil
            self.moveNumber = nil
            self.isWhiteMove = nil

            // Generate stable ID based on content and position
            switch pgnElement {
            case .whiteNumber(let number):
                self.id = "white-\(number)-\(sequenceIndex)"
            case .blackNumber(let number):
                self.id = "black-\(number)-\(sequenceIndex)"
            case .move(_, let index):
                self.id = "move-\(index)-\(sequenceIndex)"
            case .variationStart:
                self.id = "var-start-\(sequenceIndex)"
            case .variationEnd:
                self.id = "var-end-\(sequenceIndex)"
            }
        }
        
        // New initializer for moves with embedded numbers (numbers can be nil for black moves in main line)
        init(move: Move, index: MoveTree.MoveIndex, moveNumber: Int?, isWhiteMove: Bool?, sequenceIndex: Int) {
            self.pgnElement = .move(move, index)
            self.move = move
            self.index = index
            self.moveNumber = moveNumber
            self.isWhiteMove = isWhiteMove
            self.id = "move-\(index)-\(sequenceIndex)"
        }

        var displayText: String {
            if let move = self.move {
                guard let metaMove = move.metaMove else {
                    return ""
                }
                if let moveNumber = self.moveNumber, let isWhiteMove = self.isWhiteMove {
                    // Format with embedded number
                    if isWhiteMove {
                        return "\(moveNumber).\(metaMove.displayDescription)"
                    } else {
                        return "\(moveNumber)…\(metaMove.displayDescription)"
                    }
                } else {
                    // No number to display, just the move (common for black moves in main line)
                    return "\(metaMove.displayDescription)"
                }
            }
            
            // Fallback to original logic for non-move elements
            guard let pgnElement = self.pgnElement else { return "" }
            switch pgnElement {
            case .whiteNumber(let number):
                return "\(number)."
            case .blackNumber(let number):
                return "\(number)…"
            case .move(let move, _):
                return move.metaMove?.displayDescription ?? ""
            case .variationStart:
                return "("
            case .variationEnd:
                return ")"
            }
        }

        var isMove: Bool {
            return move != nil || (pgnElement != nil && { 
                if case .move = pgnElement! { return true }
                return false
            }())
        }
    }
    
    // MARK: - Enums and Structs
    enum GameStatus: Equatable {
        case inProgress
        case checkmate(Piece.Color)
        case stalemate
        case draw(Board.EndResult.DrawType)
    }

    /// Represents a variation option for user selection
    struct VariationOption: Identifiable {
        let id: Int
        let index: MoveTree.MoveIndex
        let pgnText: String
        let isMainLine: Bool
    }
    
    // MARK: - Initialization
    init() {
        // Initialize child managers first
        self.visualAnnotationManager = VisualAnnotationManager()
        
        // Now that self is fully initialized, set up the session provider
        self.visualAnnotationManager.setSessionProvider { [weak self] in
            return self?.session
        }
        
        // Debug log to confirm ChessGameViewModel initialization
        print("🎯 ChessGameViewModel initialized")
        print("🎯 EngineManager initialized: \(engineManager)")
    }
    
    /// Called by GameSession when the game state changes
    func onGameStateChanged() {
        guard let session = session else { return }
        
        // Update derived properties from session
        board = session.board
        currentMoveIndex = session.currentMoveIndex
        
        // Update last move for highlighting
        if currentMoveIndex != session.game.startingIndex {
            lastMove = session.game.moves.getNodeMove(index: currentMoveIndex)
        } else {
            lastMove = nil
        }
        
        // Clear selections
        clearAllSelections()
        
        // Trigger cache invalidation
        throttledCacheInvalidation()
        
        // Auto-analyze new position if engine is running
        if engineManager.state == .analyzing {
            Task {
                await self.engineManager.analyzePosition(self.board.position)
            }
        }
    }
    
    // MARK: - Game Actions
    
    /// Handles square selection and piece movement
    func handleSquarePress(_ square: Square) {
        if let selectedSquare = selectedSquare {
            // Normal mode with a piece already selected
            attemptMove(from: selectedSquare, to: square)
        } else {
            // No selection, try to select a piece
            selectSquare(square)
        }
    }
    
    /// Selects a square if it contains a piece of the current player
    private func selectSquare(_ square: Square) {
        guard let piece = board.position.piece(at: square),
              piece.color == board.position.sideToMove else {
            clearSelection()
            return
        }
        
        selectedSquare = square
        possibleMoves = board.legalMoves(forPieceAt: square)
    }
    
    /// Attempts to move a piece from one square to another
    func attemptMove(from startSquare: Square, to endSquare: Square) {
        // If clicking the same square, deselect
        if startSquare == endSquare {
            clearAllSelections()
            return
        }
        
        // If clicking another piece of the same color, select it instead
        if let piece = board.position.piece(at: endSquare),
           piece.color == board.position.sideToMove {
            selectSquare(endSquare)
            return
        }
        
        // Check if the move is legal first, but don't execute it yet
        if board.canMove(pieceAt: startSquare, to: endSquare) {
            // Create a temporary move to check for variations
            var tempBoard = board
            if let tempMove = tempBoard.move(pieceAt: startSquare, to: endSquare) {
                // Check if we need to show variation creation dialog before making the actual move
                if shouldShowVariationCreationDialog(for: tempMove) {
                    // Store the move and show dialog, but don't execute the move yet
                    pendingMove = tempMove
                    pendingMoveFromIndex = currentMoveIndex
                    existingNextMoveIndex = getExistingNextMoveIndex(for: tempMove)
                    showVariationCreationDialog = true
                    clearAllSelections()
                    return
                }
                
                // No variation dialog needed, execute the move directly
                if let actualMove = board.move(pieceAt: startSquare, to: endSquare) {
                    handleMoveResult(actualMove)
                }
            }
        } else {
            // Invalid move, clear all selections
            clearAllSelections()
        }
    }
    
    /// Handles the result of a successful move (move has already been executed on the board)
    private func handleMoveResult(_ move: Move) {
        lastMove = move
        
        guard let metaMove = move.metaMove else {
            return
        }
        
        // Play move sound
        soundManager.playMoveSound(for: move)
        
        // Check for pawn promotion
        if metaMove.piece.kind == .pawn &&
           (metaMove.end.rank.value == 8 || metaMove.end.rank.value == 1) &&
            metaMove.promotedPiece == nil {
            promotionMove = move
            showPromotionDialog = true
            clearAllSelections()
            return
        }
        
        // Complete the move sequence - the move has already been executed on the board
        completeExecutedMoveSequence(move)
    }
    
    /// Completes a move sequence for a move that has already been executed on the board
    private func completeExecutedMoveSequence(_ move: Move) {
        guard let session = session else { return }
        
        // Check if there's already a next move that would create a variation
        if let existingNextIndex = session.game.moves.hasNextMove(containing: move, for: currentMoveIndex) {
            // Move already exists, just navigate to it
            session.goToMove(at: existingNextIndex)
            clearAllSelections()
            return
        }
        
        // No existing moves, proceed with normal move creation in the game tree
        // (the move has already been executed on the board)
        let newIndex = session.makeMoveInGame(move, from: currentMoveIndex)
        session.goToMove(at: newIndex)
        
        clearAllSelections()
        checkGameStatus(move)
        
        // Throttled cache invalidation to prevent excessive recalculation
        throttledCacheInvalidation()
    }

    /// Completes a move and updates the game state
    private func completeMoveSequence(_ move: Move) {
        guard let session = session else { return }
        
        // Check if there's already a next move that would create a variation
        if let existingNextIndex = session.game.moves.hasNextMove(containing: move, for: currentMoveIndex) {
            // Move already exists, just navigate to it
            session.goToMove(at: existingNextIndex)
            clearAllSelections()
            return
        }
        
        // Special handling for the first move (from starting position)
        if currentMoveIndex == session.game.startingIndex {
            // Check if there's already a first move in the game
            if !session.game.moves.isEmpty {
                // There's already a first move, this will create a variation
                pendingMove = move
                pendingMoveFromIndex = currentMoveIndex
                existingNextMoveIndex = session.game.moves.nextIndex(currentIndex: currentMoveIndex)
                showVariationCreationDialog = true
                clearSelection()
                return
            }
        }
        
        // Check if there are any existing moves from this position (main line or variations)
        let mainLineNextIndex = session.game.moves.nextIndex(currentIndex: currentMoveIndex)
        let variations = session.game.moves.variations(from: currentMoveIndex)
        
        // If there's a main line move OR variations, this new move will create a variation
        if mainLineNextIndex != nil || !variations.isEmpty {
            // There are existing moves, show variation creation dialog
            pendingMove = move
            pendingMoveFromIndex = currentMoveIndex
            existingNextMoveIndex = mainLineNextIndex
            showVariationCreationDialog = true
            clearAllSelections()
            return
        }
        
        // No existing moves, proceed with normal move creation
        executeMove(move, option: .newVariation)
    }
    
    /// Executes a move with the specified variation creation option
    private func executeMove(_ move: Move, option: VariationCreationOption) {
        guard let session = session else { return }
        let fromIndex = pendingMoveFromIndex ?? currentMoveIndex
        
        guard let metaMove = move.metaMove else {
            return
        }
        
        // First, execute the move on the board
        guard let executedMove = board.move(pieceAt: metaMove.start, to: metaMove.end) else {
            print("Failed to execute move on board")
            return
        }
        
        // Update last move and play sound
        lastMove = executedMove
        soundManager.playMoveSound(for: executedMove)
        
        // Check for pawn promotion
        if executedMove.metaMove!.piece.kind == .pawn &&
            (executedMove.metaMove!.end.rank.value == 8 || executedMove.metaMove!.end.rank.value == 1) &&
            executedMove.metaMove!.promotedPiece == nil {
            promotionMove = executedMove
            showPromotionDialog = true
            clearAllSelections()
            return
        }
        
        // Add the move to the game tree based on the selected option
        switch option {
        case .newVariation:
            // Default behavior: create new variation
            let newIndex = session.makeMoveInGame(executedMove, from: fromIndex)
            session.goToMove(at: newIndex)
            
        case .newMainLine:
            // Create new variation and promote it
            let newMoveIndex = session.makeMoveInGame(executedMove, from: fromIndex)
            _ = session.promoteVariation(at: newMoveIndex)
            session.goToMove(at: newMoveIndex)
            
        case .overwrite:
            // Delete all moves after the current position (both next and children)
            _ = session.overwriteMove(executedMove, from: fromIndex)
            // goToMove is called within overwriteMove
        }
        
        clearAllSelections()
        checkGameStatus(executedMove)
        
        // Clear pending move data
        pendingMove = nil
        pendingMoveFromIndex = nil
        existingNextMoveIndex = nil
        
        // Throttled cache invalidation to prevent excessive recalculation
        throttledCacheInvalidation()
    }
    
    /// Completes a pawn promotion
    func completePromotion(to pieceKind: Piece.Kind) {
        guard let promotionMove = promotionMove else { return }
        
        let completedMove = board.completePromotion(of: promotionMove, to: pieceKind)
        
        // Since promotion moves are always created as part of executeMove, 
        // we need to complete the move sequence
        completeExecutedMoveSequence(completedMove)
        
        self.promotionMove = nil
        showPromotionDialog = false
    }
    
    /// Clears the current selection
    func clearSelection() {
        selectedSquare = nil
        possibleMoves = []
    }
    
    /// Clears all selections (normal and reverse drag)
    func clearAllSelections() {
        clearSelection()
        cancelReverseDrag()
    }
    
    // MARK: - Drag and Drop Validation
    
    /// Validates if a drag operation can start for the given piece and square
    func validateDragStart(piece: Piece, from square: Square) -> Bool {
        guard piece.color == board.position.sideToMove else { return false }
        return true
    }
    
    /// Checks if a piece at the given square can be moved (has legal moves)
    func canMovePiece(at square: Square) -> Bool {
        guard let piece = board.position.piece(at: square),
              piece.color == board.position.sideToMove else { return false }
        return !board.legalMoves(forPieceAt: square).isEmpty
    }
    
    /// Sets the selected square for move validation
    func setSelectedSquare(_ square: Square) {
        selectedSquare = square
        possibleMoves = board.legalMoves(forPieceAt: square)
    }
    
    /// Attempts to move a piece from one square to another (called from drag operations)
//    func attemptMove(from startSquare: Square, to endSquare: Square) {
//        attemptMove(from: startSquare, to: endSquare)
//    }
    
    /// Checks the game status after a move
    private func checkGameStatus(_ move: Move) {
        guard let metaMove = move.metaMove else {
            gameStatus = .inProgress
            return
        }
        switch metaMove.checkState {
        case .checkmate:
            gameStatus = .checkmate(board.position.sideToMove.opposite)
        case .stalemate:
            gameStatus = .stalemate
        default:
            // Check for other draw conditions
            if board.position.clock.halfmoves >= 100 {
                gameStatus = .draw(.fiftyMoves)
            } else if board.position.hasInsufficientMaterial {
                gameStatus = .draw(.insufficientMaterial)
            } else {
                gameStatus = .inProgress
            }
        }
    }
    
    // MARK: - Game Management
    
    /// Resets the game to the starting position
    func newGame() {
        session?.newGame()
        clearSelection()
        gameStatus = .inProgress
        promotionMove = nil
        showPromotionDialog = false
        showVariationCreationDialog = false
        pendingMove = nil
        pendingMoveFromIndex = nil
        existingNextMoveIndex = nil
        currentFilePath = nil // Clear file path for new game
        invalidateCache()
    }
    
    /// Gets the current player's turn
    var currentPlayer: Piece.Color {
        board.position.sideToMove
    }
    
    /// Gets the game's PGN representation
    var pgn: String {
        session?.pgn ?? ""
    }
    
    // MARK: - File Management (using ChessFileManager)
    
    private let fileManager = ChessFileManager.shared
    
    /// Loads a game from PGN
    func loadGame(from pgn: String) {
        session?.loadGame(from: pgn)
        clearAllSelections()
        gameStatus = .inProgress
        promotionMove = nil
        showPromotionDialog = false
        showVariationCreationDialog = false
        pendingMove = nil
        pendingMoveFromIndex = nil
        existingNextMoveIndex = nil
        invalidateCache()
    }
    
    /// Loads a game from PGN file
    func loadGame(from url: URL) {
        currentFilePath = url
        session?.loadGame(from: url)
        clearAllSelections()
        gameStatus = .inProgress
        promotionMove = nil
        showPromotionDialog = false
        showVariationCreationDialog = false
        pendingMove = nil
        pendingMoveFromIndex = nil
        existingNextMoveIndex = nil
        invalidateCache()
    }
    
    /// Loads a game from a Game object
    func loadGameFromObject(_ gameObject: Game) {
        session?.loadGameFromObject(gameObject)
        clearAllSelections()
        gameStatus = .inProgress
        promotionMove = nil
        showPromotionDialog = false
        showVariationCreationDialog = false
        pendingMove = nil
        pendingMoveFromIndex = nil
        existingNextMoveIndex = nil
        invalidateCache()
    }
    
    /// Loads a position from FEN string
    func loadPosition(from fen: String) {
        session?.loadPosition(from: fen)
        clearAllSelections()
        gameStatus = .inProgress
        promotionMove = nil
        showPromotionDialog = false
        showVariationCreationDialog = false
        pendingMove = nil
        pendingMoveFromIndex = nil
        existingNextMoveIndex = nil
        invalidateCache()
    }
    
    /// Saves the current game to the current file path
    func saveGame() throws {
        guard let filePath = currentFilePath else {
            throw GameSaveError.noCurrentFile
        }
        guard let session = session else { return }
        try fileManager.saveGame(session.game, to: filePath)
    }
    
    /// Saves the current game to a new file path
    func saveGame(to url: URL) throws {
        currentFilePath = url
        guard let session = session else { return }
        try fileManager.saveGame(session.game, to: url)
    }
    
    /// Loads game from clipboard
    func loadGameFromClipboard() {
        guard let session = session else { return }
        
        if session.loadGameFromClipboard() {
            // Successfully loaded from clipboard with undo support
            clearAllSelections()
            gameStatus = .inProgress
            promotionMove = nil
            showPromotionDialog = false
            showVariationCreationDialog = false
            pendingMove = nil
            pendingMoveFromIndex = nil
            existingNextMoveIndex = nil
            invalidateCache()
        } else {
            // Failed to load - show error
            showImportErrorAlert = true
        }
    }
    
    /// Opens the position editor
    func openPositionEditor() {
        showPositionEditor = true
    }
    
    /// Handles setting a new position from the position editor
    func setPosition(_ position: Position, shouldFlip: Bool = false) {
        guard let session = session else { return }
        
        if session.setPosition(position, shouldFlip: shouldFlip) {
            // Successfully set position with undo support
            clearAllSelections()
            gameStatus = .inProgress
            promotionMove = nil
            showPromotionDialog = false
            showVariationCreationDialog = false
            pendingMove = nil
            pendingMoveFromIndex = nil
            existingNextMoveIndex = nil
            invalidateCache()
        }

        showPositionEditor = false
    }
}

/// MARK: - Move Editing Extension
extension ChessGameViewModel {

    /// Shows the edit menu for a specific move
    func showEditMenu(for moveIndex: MoveTree.MoveIndex, at position: CGPoint) {
        self.selectedMoveForEdit = moveIndex
        self.editMenuPosition = position
        self.showMoveEditMenu = true
    }

    /// Hides the edit menu
    func hideEditMenu() {
        self.showMoveEditMenu = false
        self.selectedMoveForEdit = nil
    }

    /// Deletes a move and all subsequent moves
    func deleteMove(at index: MoveTree.MoveIndex) {
        guard session?.deleteMove(at: index) == true else {
            return
        }
        
        invalidateCache()
        hideEditMenu()
    }
    
    func deleteVariation(at index: MoveTree.MoveIndex) {
        guard let session = session else { return }
        if let rootNodeIndex = session.game.moves.getVariationRootNodeIndex(index: index) {
            deleteMove(at: rootNodeIndex)
        }
    }
    
    /// Deletes all moves before the specified move, making it the new starting position
    func deleteBeforeMove(at index: MoveTree.MoveIndex) {
        guard session?.deleteBeforeMove(at: index) == true else {
            return
        }
        
        invalidateCache()
        hideEditMenu()
    }

    /// Promotes a variation's priority
    func promoteVariation(at index: MoveTree.MoveIndex) {
        guard session?.promoteVariation(at: index) == true else {
            return
        }

        invalidateCache()
        hideEditMenu()
    }

    /// Promotes a variation to main variation
    func promoteToMainVariation(at index: MoveTree.MoveIndex) {
        guard session?.promoteToMainVariation(at: index) == true else {
            return
        }

        invalidateCache()
        hideEditMenu()
    }

    /// Finds the last valid position before a given index
    private func findLastValidPosition(before index: MoveTree.MoveIndex) -> MoveTree.MoveIndex? {
        guard let session = session else { return nil }
        let history = session.game.moves.history(for: index)

        // Go through history in reverse to find the last valid position
        for historyIndex in history.reversed() {
            if session.game.positions[historyIndex] != nil {
                return historyIndex
            }
        }

        return session.game.startingIndex
    }


    /// Checks if a variation can be promoted (not already main variation)
    func canPromoteVariation(at index: MoveTree.MoveIndex) -> Bool {
        guard let session = session else { return false }
        return !session.game.moves.isOnMainVariation(index: index)
    }
}

/// MARK: - Error Types
enum GameSaveError: Error {
    case noCurrentFile
}

/// MARK: - Move Navigation Extension
extension ChessGameViewModel {
    
    /// Goes to the previous move in the game
    func goToPreviousMove() {
        session?.goToPreviousMove()
    }
    
    /// Goes to the next move in the game
    func goToNextMove() {
        guard let session = session else { return }
        if !canGoToNextMove {
            return
        }
        
        // Get available next moves from session
        let nextMoves = session.goToNextMove()
        
        if nextMoves.isEmpty {
            return
        }
        
        if nextMoves.count == 1 {
            // Only one option, go directly
            session.goToMove(at: nextMoves[0])
        } else {
            // Multiple options, show selection dialog
            let mainLineIndex = nextMoves[0]
            let variationIndices = Array(nextMoves.dropFirst())
            showVariationSelectionDialog(mainLineIndex: mainLineIndex, variationIndices: variationIndices)
        }
    }
    
    /// Shows the variation selection dialog
    private func showVariationSelectionDialog(mainLineIndex: MoveTree.MoveIndex?, variationIndices: [MoveTree.MoveIndex]) {
        var options: [VariationOption] = []
        
        // Add main line option if available
        if let mainLineIndex = mainLineIndex {
            let mainLinePGN = generatePGNTextForMove(at: mainLineIndex)
            options.append(VariationOption(
                id: 0,
                index: mainLineIndex,
                pgnText: mainLinePGN,
                isMainLine: true
            ))
        }
        
        // Add variation options
        for (i, variationIndex) in variationIndices.enumerated() {
            let variationPGN = generatePGNTextForMove(at: variationIndex)
            options.append(VariationOption(
                id: i+1,
                index: variationIndex,
                pgnText: variationPGN,
                isMainLine: false
            ))
        }
        
        // Sort by main line first, then by move hash for consistent ordering
        options.sort(by: { lhs, rhs in
            lhs.id < rhs.id
        })
        
        self.availableVariations = options
        self.isKeyboardNavigationDisabled = true  // Disable global navigation shortcuts
        self.showVariationSelection = true
    }
    
    /// Generates PGN text for a move and its continuation
    private func generatePGNTextForMove(at index: MoveTree.MoveIndex) -> String {
        guard let session = session else { return "" }
        
        // Use the variationPGN method to get the proper PGN representation
        let pgnElements = session.game.moves.variationPGN(from: index)
        
        var result = ""
        var moveCount = 0
        let maxMovesToShow = 6 // Show up to 3 move pairs
        
        for element in pgnElements {
            if moveCount >= maxMovesToShow { break }
            
            switch element {
            case .whiteNumber(let number):
                if !result.isEmpty { result += " " }
                result += "\(number)."
                
            case .blackNumber(let number):
                if moveCount != 0 {
                    break
                }
                if !result.isEmpty { result += " " }
                result += "\(number)…"
                
            case .move(let move, _):
                if !result.isEmpty && !result.hasSuffix(".") && !result.hasSuffix("…") {
                    result += " "
                }
                result += move.metaMove!.displayDescription
                moveCount += 1
                
            case .variationStart, .variationEnd:
                // Skip variation markers for this display
                break
            }
        }
        
        return result
    }
    
    /// Selects a variation and navigates to it
    func selectVariation(_ option: VariationOption) {
        self.showVariationSelection = false
        self.availableVariations = []
        self.isKeyboardNavigationDisabled = false  // Re-enable global navigation shortcuts
        
        // Navigate to the selected variation through the session
        self.session?.goToMove(at: option.index)
    }
    
    /// Cancels variation selection
    func cancelVariationSelection() {
        self.showVariationSelection = false
        self.availableVariations = []
        self.isKeyboardNavigationDisabled = false  // Re-enable global navigation shortcuts
    }
    
    /// Goes to the start of the game
    func goToStart() {
        session?.goToStart()
    }
    
    /// Goes to the end of the game (last move played)
    func goToEnd() {
        session?.goToEnd()
    }
    
    /// Goes to a specific move index
    func goToMove(at index: MoveTree.MoveIndex) {
        session?.goToMove(at: index)
    }
    
    /// Updates the board position to match the current move index (deprecated - handled by GameSession)
    @MainActor
    private func updateBoardToCurrentIndex() async {
        // This method is deprecated - GameSession handles board updates through onGameStateChanged
        onGameStateChanged()
    }
    
    /// Helper method to find a move from position differences
    private func findMoveFromPositions(from previousPosition: Position, to currentPosition: Position) {
        // Compare piece positions to find the move
        let previousPieces = previousPosition.pieces
        let currentPieces = currentPosition.pieces
        
        // Find pieces that moved
        for piece in previousPieces {
            if !currentPieces.contains(where: { $0.square == piece.square && $0.kind == piece.kind && $0.color == piece.color }) {
                // This piece moved, find where it went
                if let movedPiece = currentPieces.first(where: { $0.kind == piece.kind && $0.color == piece.color && !previousPieces.contains(where: { $0.square == $0.square && $0.kind == piece.kind && $0.color == piece.color }) }) {
                    // Create a move object for highlighting
                    let move = Move(metaMove: MetaMove(
                        result: .move,
                        piece: piece,
                        start: piece.square,
                        end: movedPiece.square
                    ))
                    lastMove = move
                    return
                }
            }
        }
    }
    
    /// Gets all moves in the game for display in the notation view (cached for performance)
    var cachedMoves: [MoveDisplayItem] {
        if !_cacheValid {
            _cachedMoves = buildAllMovesWithVariations()
            _cacheValid = true
        }
        return _cachedMoves
    }
    
    /// Invalidates the move cache
    private func invalidateCache() {
        _cacheValid = false
        _lastCurrentMoveIndex = nil // Reset current move tracking when content changes
    }
    
    #if DEBUG
    internal func invalidateCacheTest() {
        invalidateCache()
    }
    #endif
    
    /// Throttled cache invalidation to prevent excessive recalculation during rapid moves
    private func throttledCacheInvalidation() {
        let now = Date()
        if now.timeIntervalSince(_lastCacheInvalidationTime) >= _cacheInvalidationThreshold {
            invalidateCache()
            _lastCacheInvalidationTime = now
        }
    }
    
    /// Builds all moves in the game including variations, filtering out numbers and adding them to moves
    private func buildAllMovesWithVariations() -> [MoveDisplayItem] {
        guard let session = session else { return [] }
        
        // Use ChessKit's pgnRepresentation to get the proper tree structure
        let pgnElements = session.game.moves.pgnRepresentation
        
        var result: [MoveDisplayItem] = []
        var sequenceIndex = 0
        // Track move numbers for each variation level
        var moveNumber = session.game.moves.initialNumber
        var whiteToMove = session.game.moves.initialSideToMove == .white
        var isInVariation = false
        var variationDepth = 0
        var justExitedVariation = false
        let StartingWithBlackToMove = !whiteToMove
        
        for pgnElement in pgnElements {
            switch pgnElement {
            case .whiteNumber(let number):
                moveNumber = number
                whiteToMove = true
            case .blackNumber(let number):
                moveNumber = number
                whiteToMove = false
            case .move(let move, let index):
                // Determine if we should show the number for this move
                let shouldShowNumber: Bool
                if whiteToMove {
                    // White moves always show number
                    shouldShowNumber = true
                } else {
                    let initialBlackToMove = StartingWithBlackToMove && index == session.game.moves.firstIndex
                    // Black moves show number in variations OR when just exited variation back to main line OR when the starting position is black to move
                    shouldShowNumber = isInVariation || justExitedVariation || initialBlackToMove
                }
                
                // Create a move item with embedded number (or nil if not showing)
                let moveWithNumber = MoveDisplayItem(
                    move: move,
                    index: index,
                    moveNumber: shouldShowNumber ? moveNumber: nil,
                    isWhiteMove: whiteToMove,
                    sequenceIndex: sequenceIndex
                )
                result.append(moveWithNumber)
                
                // Reset the justExitedVariation flag after processing a move
                justExitedVariation = false
                sequenceIndex += 1
                
            case .variationStart:
                let item = MoveDisplayItem(pgnElement: pgnElement, sequenceIndex: sequenceIndex)
                result.append(item)
                variationDepth += 1
                isInVariation = true
                sequenceIndex += 1
                
            case .variationEnd:
                let item = MoveDisplayItem(pgnElement: pgnElement, sequenceIndex: sequenceIndex)
                result.append(item)
                variationDepth -= 1
                
                // When exiting a variation back to main line (variationDepth == 0), 
                // mark that we just exited so the next move shows number if it's black
                if variationDepth == 0 {
                    isInVariation = false
                    justExitedVariation = true
                } else {
                    isInVariation = true
                }
                
                sequenceIndex += 1
            }
        }
        
        return result
    }
    
    /// Checks if we can go to the previous move
    var canGoToPreviousMove: Bool {
        return session?.canGoToPreviousMove ?? false
    }
    
    /// Checks if we can go to the next move
    var canGoToNextMove: Bool {
        return session?.canGoToNextMove ?? false
    }
    
    var currentPosition: Position? {
        return session?.currentPosition
    }
    
    /// Gets the current move number for display
    var currentMoveNumber: String {
        guard let session = session else { return "Start" }
        
        if currentMoveIndex == session.game.startingIndex {
            return "Start"
        }
        
        let moveNumber = session.game.moves.getNodeNumber(index: currentMoveIndex)!
        let color = session.game.moves.getNodeColor(index: currentMoveIndex)!
        return color == .white ? "\(moveNumber)." : "\(moveNumber)…"
    }
    
    /// Handles variation creation option selection
    func selectVariationCreationOption(_ option: VariationCreationOption) {
        guard let move = pendingMove else { return }
        
        self.showVariationCreationDialog = false
        self.isKeyboardNavigationDisabled = false  // Re-enable navigation
        
        // Add a small delay to ensure UI is fully reset after sheet dismissal
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.executeMove(move, option: option)
        }
    }
    
    /// Cancels variation creation dialog
    func cancelVariationCreation() {
        self.showVariationCreationDialog = false
        self.isKeyboardNavigationDisabled = false  // Re-enable navigation
        self.pendingMove = nil
        self.pendingMoveFromIndex = nil
        self.existingNextMoveIndex = nil
        
        // Restore the board to the current move index position
        // This ensures the board shows the correct state after cancellation
        Task {
            await self.updateBoardToCurrentIndex()
        }
    }
    
    /// Finds all squares containing pieces that can move to the target square
    private func findSourceSquares(for targetSquare: Square) -> [Square] {
        var sourceSquares: [Square] = []
        
        // Check all squares on the board
        for rank in 1...8 {
            for file in ["a", "b", "c", "d", "e", "f", "g", "h"] {
                let square = Square("\(file)\(rank)")
                let piece = board.position.piece(at: square)
                
                // Only check pieces of the current player
                guard let piece = piece, piece.color == board.position.sideToMove else { continue }
                
                // Check if this piece can move to the target square
                let legalMoves = board.legalMoves(forPieceAt: square)
                if legalMoves.contains(targetSquare) {
                    sourceSquares.append(square)
                }
            }
        }
        
        print("findSourceSquares for \(targetSquare): found \(sourceSquares.count) pieces - \(sourceSquares)")
        return sourceSquares
    }
    
    /// Starts a reverse drag from a target square
    func startReverseDrag(from targetSquare: Square) -> Bool {
        let piece = board.position.piece(at: targetSquare)
        
        print("startReverseDrag from \(targetSquare), piece: \(piece?.description ?? "empty"), sideToMove: \(board.position.sideToMove)")
        
        // Only allow reverse drag from empty squares or opponent pieces
        if piece == nil || piece?.color != board.position.sideToMove {
            let validSources = findSourceSquares(for: targetSquare)
            
            // Enter reverse drag mode
            isReverseDragActive = true
            reverseDragTarget = targetSquare
            reverseDragValidSources = validSources
            clearSelection() // Clear normal selection
            
            print("startReverseDrag success, found \(validSources.count) source pieces: \(validSources)")
            return true
        }
        print("startReverseDrag failed - trying to drag own piece")
        return false
    }
    
    /// Completes a reverse drag to a source square
    func completeReverseDrag(to sourceSquare: Square) {
        guard isReverseDragActive,
              let targetSquare = reverseDragTarget else {
            cancelReverseDrag()
            return
        }
        
        print("completeReverseDrag from \(sourceSquare) to \(targetSquare)")
        
        // Check if the source square is valid for this reverse drag
        if reverseDragValidSources.contains(sourceSquare) {
            print("Valid reverse drag move, attempting move from \(sourceSquare) to \(targetSquare)")
            attemptMove(from: sourceSquare, to: targetSquare)
        } else {
            print("Invalid reverse drag move - source \(sourceSquare) not in valid sources: \(reverseDragValidSources)")
        }
        
        cancelReverseDrag()
    }
    
    /// Cancels the reverse drag operation
    func cancelReverseDrag() {
        isReverseDragActive = false
        reverseDragTarget = nil
        reverseDragValidSources = []
    }
    
    /// Checks if a variation creation dialog should be shown for the given move
    private func shouldShowVariationCreationDialog(for move: Move) -> Bool {
        guard let session = session else { return false }
        
        // Check if there's already a next move that would create a variation
        if session.game.moves.hasNextMove(containing: move, for: currentMoveIndex) != nil {
            // Move already exists, no dialog needed
            return false
        }
        
        // Special handling for the first move (from starting position)
        if currentMoveIndex == session.game.startingIndex {
            // Check if there's already a first move in the game
            if !session.game.moves.isEmpty {
                // There's already a first move, this will create a variation
                return true
            }
        }
        
        // Check if there are any existing moves from this position (main line or variations)
        let mainLineNextIndex = session.game.moves.nextIndex(currentIndex: currentMoveIndex)
        let variations = session.game.moves.variations(from: currentMoveIndex)
        
        // If there's a main line move OR variations, this new move will create a variation
        return mainLineNextIndex != nil || !variations.isEmpty
    }
    
    /// Gets the existing next move index for the current position
    private func getExistingNextMoveIndex(for move: Move) -> MoveTree.MoveIndex? {
        guard let session = session else { return nil }
        
        if currentMoveIndex == session.game.startingIndex {
            return session.game.moves.nextIndex(currentIndex: currentMoveIndex)
        }
        return session.game.moves.nextIndex(currentIndex: currentMoveIndex)
    }
    
    // MARK: - PGN Metadata
    
    /// Formats player information with rating if available
    func formatPlayerInfo(name: String, color: Piece.Color) -> String {
        if name.isEmpty {
            return color == .white ? "White" : "Black"
        }

        // Extract rating from name if it contains rating info
        // Format: "Player Name (2500)" or "Player Name"
        let ratingPattern = #"\((\d+)\)"#
        if let ratingMatch = name.range(of: ratingPattern, options: .regularExpression) {
            let rating = String(name[ratingMatch]).replacingOccurrences(of: "(", with: "").replacingOccurrences(of: ")", with: "")
            let nameWithoutRating = name.replacingOccurrences(of: ratingPattern, with: "", options: .regularExpression).trimmingCharacters(in: .whitespaces)
            return "\(nameWithoutRating) \(rating)"
        }

        // Check for rating in other tags
        let ratingKey = color == .white ? "WhiteElo" : "BlackElo"
        if let session = session, let rating = session.game.tags.other[ratingKey], !rating.isEmpty {
            return "\(name) \(rating)"
        }

        return name
    }
    
    // MARK: - Assessment Methods
    
    /// Sets the move assessment for a move at the given index
    func setMoveAssessment(_ assessment: MetaMove.Assessment, at moveIndex: MoveTree.MoveIndex) {
        guard let session = session else { return }

        if var move = session.game.moves.getNodeMove(index: moveIndex) {
            move.metaMove!.moveAssessment = assessment
            session.isModified = true
            _ = session.editMove(at: moveIndex, newMove: move)
        }

        // Trigger UI update
        objectWillChange.send()

        // Invalidate cache to refresh move notation display
        invalidateCache()
    }
    
    /// Sets the position assessment for a move at the given index
    func setPositionAssessment(_ assessment: MetaMove.Assessment, at moveIndex: MoveTree.MoveIndex) {
        guard let session = session else { return }

        if var move = session.game.moves.getNodeMove(index: moveIndex) {
            move.metaMove!.positionAssessment = assessment
            session.isModified = true
            _ = session.editMove(at: moveIndex, newMove: move)
        }

        // Trigger UI update
        objectWillChange.send()

        // Invalidate cache to refresh move notation display
        invalidateCache()
    }
    
    /// Sets the comment text for a move at the given index
    func setMoveCommentText(_ text: String, at moveIndex: MoveTree.MoveIndex) {
        guard let session = session else { return }

        if var move = session.game.moves.getNodeMove(index: moveIndex) {
            move.positionComment.text = text
            session.isModified = true
            _ = session.editMove(at: moveIndex, newMove: move)
        }

        // Trigger UI update
        objectWillChange.send()

        // Invalidate cache to refresh move notation display
        invalidateCache()
    }
    
    /// Gets the current comment text at the given index
    func getMoveCommentText(at moveIndex: MoveTree.MoveIndex) -> String {
        guard let session = session else { return "" }
        return session.game.moves.getNodeMove(index: moveIndex)?.positionComment.text ?? ""
    }
    
    // MARK: - Visual Annotations Editing
    
    /// Toggles the board orientation
    func toggleBoardFlip() {
        self.isBoardFlipped.toggle()
        print("Board flipped: \(self.isBoardFlipped)")
    }
}

// MARK: - Metadata Binding Extensions
extension ChessGameViewModel {
    
    /// Creates a binding for any metadata field using KeyPath
    func metadataBinding<T>(_ keyPath: WritableKeyPath<Game.Metadata, T>) -> Binding<T> {
        Binding(
            get: { self.session?.game.metadata[keyPath: keyPath] ?? Game.Metadata()[keyPath: keyPath] },
            set: {
                self.session?.game.metadata[keyPath: keyPath] = $0
                self.session?.isModified = true
                // Trigger UI update when metadata changes
                self.objectWillChange.send()
            }
        )
    }
    
    /// Convenience bindings for common metadata fields
    var eventBinding: Binding<String> { metadataBinding(\.event) }
    var siteBinding: Binding<String> { metadataBinding(\.site) }
    var dateBinding: Binding<String> { metadataBinding(\.date) }
    var roundBinding: Binding<String> { metadataBinding(\.round) }
    var whiteBinding: Binding<String> { metadataBinding(\.white) }
    var blackBinding: Binding<String> { metadataBinding(\.black) }
    var whiteTeamBinding: Binding<String> { metadataBinding(\.whiteTeam) }
    var blackTeamBinding: Binding<String> { metadataBinding(\.blackTeam) }
    var whiteTitleBinding: Binding<String> { metadataBinding(\.whiteTitle) }
    var blackTitleBinding: Binding<String> { metadataBinding(\.blackTitle) }
    var whiteEloBinding: Binding<String> { metadataBinding(\.whiteElo) }
    var blackEloBinding: Binding<String> { metadataBinding(\.blackElo) }
    var whiteFideIdBinding: Binding<String> { metadataBinding(\.whiteFideId) }
    var blackFideIdBinding: Binding<String> { metadataBinding(\.blackFideId) }
    var ecoBinding: Binding<String> { metadataBinding(\.eco) }
    var openingBinding: Binding<String> { metadataBinding(\.opening) }
    var timeControlBinding: Binding<String> { metadataBinding(\.timeControl) }
    var notesBinding: Binding<String> { metadataBinding(\.notes) }
    var setUpBinding: Binding<String> { metadataBinding(\.setUp) }
    var fenBinding: Binding<String> { metadataBinding(\.fen) }
    
    /// Special binding for result field with default value handling
    var resultBinding: Binding<String> {
        Binding(
            get: {
                let result = self.session?.game.metadata.result ?? ""
                return result.isEmpty ? "*" : result
            },
            set: {
                self.session?.game.metadata.result = $0
                self.session?.isModified = true
                self.objectWillChange.send()
            }
        )
    }
    
    /// Auto-detects game result based on current game status
    func autoDetectGameResult() {
        guard let session = session else { return }
        
        // Only update if result is currently unset
        if session.game.metadata.result.isEmpty || session.game.metadata.result == "*" {
            switch gameStatus {
            case .checkmate(let color):
                session.game.metadata.result = color == .white ? "1-0" : "0-1"
            case .stalemate, .draw:
                session.game.metadata.result = "1/2-1/2"
            case .inProgress:
                session.game.metadata.result = "*"
            }
        }
    }
}
